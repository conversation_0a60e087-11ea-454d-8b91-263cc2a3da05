{"version": 3, "file": "locks.js", "sourceRoot": "", "sources": ["../../../src/lib/locks.ts"], "names": [], "mappings": ";;;AAAA,uCAAgD;AAEhD;;GAEG;AACU,QAAA,SAAS,GAAG;IACvB;;OAEG;IACH,KAAK,EAAE,CAAC,CAAC,CACP,UAAU;QACV,IAAA,8BAAoB,GAAE;QACtB,UAAU,CAAC,YAAY;QACvB,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,gCAAgC,CAAC,KAAK,MAAM,CAC7E;CACF,CAAA;AAED;;;;GAIG;AACH,MAAsB,uBAAwB,SAAQ,KAAK;IAGzD,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAA;QAHA,qBAAgB,GAAG,IAAI,CAAA;IAIvC,CAAC;CACF;AAND,0DAMC;AAED,MAAa,gCAAiC,SAAQ,uBAAuB;CAAG;AAAhF,4EAAgF;AAChF,MAAa,8BAA+B,SAAQ,uBAAuB;CAAG;AAA9E,wEAA8E;AAE9E;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACI,KAAK,UAAU,aAAa,CACjC,IAAY,EACZ,cAAsB,EACtB,EAAoB;IAEpB,IAAI,iBAAS,CAAC,KAAK,EAAE;QACnB,OAAO,CAAC,GAAG,CAAC,kDAAkD,EAAE,IAAI,EAAE,cAAc,CAAC,CAAA;KACtF;IAED,MAAM,eAAe,GAAG,IAAI,UAAU,CAAC,eAAe,EAAE,CAAA;IAExD,IAAI,cAAc,GAAG,CAAC,EAAE;QACtB,UAAU,CAAC,GAAG,EAAE;YACd,eAAe,CAAC,KAAK,EAAE,CAAA;YACvB,IAAI,iBAAS,CAAC,KAAK,EAAE;gBACnB,OAAO,CAAC,GAAG,CAAC,sDAAsD,EAAE,IAAI,CAAC,CAAA;aAC1E;QACH,CAAC,EAAE,cAAc,CAAC,CAAA;KACnB;IAED,oFAAoF;IAEpF,0EAA0E;IAC1E,yEAAyE;IACzE,0EAA0E;IAC1E,2EAA2E;IAC3E,6EAA6E;IAC7E,wEAAwE;IACxE,UAAU;IACV,OAAO,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CACvC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAChC,IAAI,EACJ,cAAc,KAAK,CAAC;QAClB,CAAC,CAAC;YACE,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,IAAI;SAClB;QACH,CAAC,CAAC;YACE,IAAI,EAAE,WAAW;YACjB,MAAM,EAAE,eAAe,CAAC,MAAM;SAC/B,EACL,KAAK,EAAE,IAAI,EAAE,EAAE;QACb,IAAI,IAAI,EAAE;YACR,IAAI,iBAAS,CAAC,KAAK,EAAE;gBACnB,OAAO,CAAC,GAAG,CAAC,8CAA8C,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;aAC7E;YAED,IAAI;gBACF,OAAO,MAAM,EAAE,EAAE,CAAA;aAClB;oBAAS;gBACR,IAAI,iBAAS,CAAC,KAAK,EAAE;oBACnB,OAAO,CAAC,GAAG,CAAC,8CAA8C,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;iBAC7E;aACF;SACF;aAAM;YACL,IAAI,cAAc,KAAK,CAAC,EAAE;gBACxB,IAAI,iBAAS,CAAC,KAAK,EAAE;oBACnB,OAAO,CAAC,GAAG,CAAC,+DAA+D,EAAE,IAAI,CAAC,CAAA;iBACnF;gBAED,MAAM,IAAI,gCAAgC,CACxC,sDAAsD,IAAI,sBAAsB,CACjF,CAAA;aACF;iBAAM;gBACL,IAAI,iBAAS,CAAC,KAAK,EAAE;oBACnB,IAAI;wBACF,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;wBAEvD,OAAO,CAAC,GAAG,CACT,kDAAkD,EAClD,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CACnC,CAAA;qBACF;oBAAC,OAAO,CAAM,EAAE;wBACf,OAAO,CAAC,IAAI,CACV,sEAAsE,EACtE,CAAC,CACF,CAAA;qBACF;iBACF;gBAED,8DAA8D;gBAC9D,iEAAiE;gBACjE,qEAAqE;gBACrE,iDAAiD;gBACjD,OAAO,CAAC,IAAI,CACV,yPAAyP,CAC1P,CAAA;gBAED,OAAO,MAAM,EAAE,EAAE,CAAA;aAClB;SACF;IACH,CAAC,CACF,CACF,CAAA;AACH,CAAC;AA9FD,sCA8FC;AAED,MAAM,aAAa,GAAqC,EAAE,CAAA;AAE1D;;;;;;;;;;;;;GAaG;AACI,KAAK,UAAU,WAAW,CAC/B,IAAY,EACZ,cAAsB,EACtB,EAAoB;;IAEpB,MAAM,iBAAiB,GAAG,MAAA,aAAa,CAAC,IAAI,CAAC,mCAAI,OAAO,CAAC,OAAO,EAAE,CAAA;IAElE,MAAM,gBAAgB,GAAG,OAAO,CAAC,IAAI,CACnC;QACE,iBAAiB,CAAC,KAAK,CAAC,GAAG,EAAE;YAC3B,kEAAkE;YAClE,OAAO,IAAI,CAAA;QACb,CAAC,CAAC;QACF,cAAc,IAAI,CAAC;YACjB,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;gBACxB,UAAU,CAAC,GAAG,EAAE;oBACd,MAAM,CACJ,IAAI,8BAA8B,CAChC,oCAAoC,IAAI,aAAa,CACtD,CACF,CAAA;gBACH,CAAC,EAAE,cAAc,CAAC,CAAA;YACpB,CAAC,CAAC;YACJ,CAAC,CAAC,IAAI;KACT,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CACnB;SACE,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;QAChB,IAAI,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE;YAC3B,MAAM,CAAC,CAAA;SACR;QAED,OAAO,IAAI,CAAA;IACb,CAAC,CAAC;SACD,IAAI,CAAC,KAAK,IAAI,EAAE;QACf,uEAAuE;QACvE,sDAAsD;QACtD,OAAO,MAAM,EAAE,EAAE,CAAA;IACnB,CAAC,CAAC,CAAA;IAEJ,aAAa,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,KAAK,CAAC,KAAK,EAAE,CAAM,EAAE,EAAE;QAC5D,IAAI,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE;YAC3B,wEAAwE;YACxE,kEAAkE;YAClE,MAAM,iBAAiB,CAAA;YAEvB,OAAO,IAAI,CAAA;SACZ;QAED,MAAM,CAAC,CAAA;IACT,CAAC,CAAC,CAAA;IAEF,yEAAyE;IACzE,yCAAyC;IACzC,OAAO,MAAM,gBAAgB,CAAA;AAC/B,CAAC;AAtDD,kCAsDC"}