const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Service role key for backend
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY; // Anonymous key for client operations

// Validate required environment variables
if (!supabaseUrl) {
  throw new Error('SUPABASE_URL environment variable is required');
}

if (!supabaseServiceKey) {
  throw new Error('SUPABASE_SERVICE_ROLE_KEY environment variable is required');
}

if (!supabaseAnonKey) {
  throw new Error('SUPABASE_ANON_KEY environment variable is required');
}

// Create Supabase client with service role key (for backend operations)
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Create Supabase client with anonymous key (for client-like operations)
const supabaseClient = createClient(supabaseUrl, supabaseAnonKey);

// Database helper functions
const SupabaseHelpers = {
  // Create a record
  async create(table, data) {
    try {
      const { data: result, error } = await supabaseAdmin
        .from(table)
        .insert(data)
        .select()
        .single();
      
      if (error) {
        console.error(`Error creating record in ${table}:`, error);
        throw error;
      }
      
      return result;
    } catch (error) {
      console.error(`Error in create operation for ${table}:`, error);
      throw error;
    }
  },

  // Find a record by ID
  async findById(table, id) {
    try {
      const { data, error } = await supabaseAdmin
        .from(table)
        .select('*')
        .eq('id', id)
        .single();
      
      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        console.error(`Error finding record by ID in ${table}:`, error);
        throw error;
      }
      
      return data;
    } catch (error) {
      console.error(`Error in findById operation for ${table}:`, error);
      throw error;
    }
  },

  // Find one record by condition
  async findOne(table, condition) {
    try {
      const { field, operator, value } = condition;
      const { data, error } = await supabaseAdmin
        .from(table)
        .select('*')
        .filter(field, operator, value)
        .single();
      
      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        console.error(`Error finding record in ${table}:`, error);
        throw error;
      }
      
      return data;
    } catch (error) {
      console.error(`Error in findOne operation for ${table}:`, error);
      throw error;
    }
  },

  // Find multiple records
  async find(table, conditions = [], options = {}) {
    try {
      let query = supabaseAdmin.from(table).select('*');
      
      // Apply conditions
      conditions.forEach(condition => {
        const { field, operator, value } = condition;
        query = query.filter(field, operator, value);
      });
      
      // Apply options
      if (options.orderBy) {
        query = query.order(options.orderBy.field, { ascending: options.orderBy.ascending !== false });
      }
      
      if (options.limit) {
        query = query.limit(options.limit);
      }
      
      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 1000) - 1);
      }
      
      const { data, error } = await query;
      
      if (error) {
        console.error(`Error finding records in ${table}:`, error);
        throw error;
      }
      
      return data || [];
    } catch (error) {
      console.error(`Error in find operation for ${table}:`, error);
      throw error;
    }
  },

  // Update a record
  async update(table, id, data) {
    try {
      const updateData = {
        ...data,
        updated_at: new Date().toISOString()
      };
      
      const { data: result, error } = await supabaseAdmin
        .from(table)
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
      
      if (error) {
        console.error(`Error updating record in ${table}:`, error);
        throw error;
      }
      
      return result;
    } catch (error) {
      console.error(`Error in update operation for ${table}:`, error);
      throw error;
    }
  },

  // Delete a record
  async delete(table, id) {
    try {
      const { error } = await supabaseAdmin
        .from(table)
        .delete()
        .eq('id', id);
      
      if (error) {
        console.error(`Error deleting record in ${table}:`, error);
        throw error;
      }
      
      return true;
    } catch (error) {
      console.error(`Error in delete operation for ${table}:`, error);
      throw error;
    }
  },

  // Get all records from a table
  async getAll(table, options = {}) {
    return this.find(table, [], options);
  },

  // Count records
  async count(table, conditions = []) {
    try {
      let query = supabaseAdmin.from(table).select('*', { count: 'exact', head: true });
      
      // Apply conditions
      conditions.forEach(condition => {
        const { field, operator, value } = condition;
        query = query.filter(field, operator, value);
      });
      
      const { count, error } = await query;
      
      if (error) {
        console.error(`Error counting records in ${table}:`, error);
        throw error;
      }
      
      return count || 0;
    } catch (error) {
      console.error(`Error in count operation for ${table}:`, error);
      throw error;
    }
  }
};

// Authentication helpers
const SupabaseAuth = {
  // Verify JWT token
  async verifyToken(token) {
    try {
      const { data: { user }, error } = await supabaseAdmin.auth.getUser(token);
      
      if (error) {
        console.error('Error verifying token:', error);
        return null;
      }
      
      return user;
    } catch (error) {
      console.error('Error in token verification:', error);
      return null;
    }
  },

  // Get user by ID
  async getUserById(userId) {
    try {
      const { data: { user }, error } = await supabaseAdmin.auth.admin.getUserById(userId);
      
      if (error) {
        console.error('Error getting user by ID:', error);
        return null;
      }
      
      return user;
    } catch (error) {
      console.error('Error in getUserById:', error);
      return null;
    }
  },

  // Create user
  async createUser(email, password, metadata = {}) {
    try {
      const { data, error } = await supabaseAdmin.auth.admin.createUser({
        email,
        password,
        user_metadata: metadata,
        email_confirm: true
      });
      
      if (error) {
        console.error('Error creating user:', error);
        throw error;
      }
      
      return data.user;
    } catch (error) {
      console.error('Error in createUser:', error);
      throw error;
    }
  },

  // Update user
  async updateUser(userId, updates) {
    try {
      const { data, error } = await supabaseAdmin.auth.admin.updateUserById(userId, updates);
      
      if (error) {
        console.error('Error updating user:', error);
        throw error;
      }
      
      return data.user;
    } catch (error) {
      console.error('Error in updateUser:', error);
      throw error;
    }
  },

  // Delete user
  async deleteUser(userId) {
    try {
      const { error } = await supabaseAdmin.auth.admin.deleteUser(userId);
      
      if (error) {
        console.error('Error deleting user:', error);
        throw error;
      }
      
      return true;
    } catch (error) {
      console.error('Error in deleteUser:', error);
      throw error;
    }
  }
};

module.exports = {
  supabaseAdmin,
  supabaseClient,
  SupabaseHelpers,
  SupabaseAuth,
  supabaseUrl,
  supabaseAnonKey
};
