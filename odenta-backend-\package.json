{"name": "dentlyzer-backend", "version": "1.0.0", "description": "Backend API for ODenta dental management system", "main": "server.js", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"start": "node start.js", "dev": "nodemon start.js", "test": "echo 'No tests specified'", "seed": "node seed.js", "seed:universities": "node seedUniversity.js", "seed:supervisors": "node seedSupervisor.js", "seed:aiu-patients": "node seedAIUPatients.js", "clear:patients": "node clearPatients.js", "migrate:firebase": "node scripts/migrateToFirebase.js", "create:superadmins": "node scripts/createSuperadmins.js"}, "keywords": ["dental", "management", "api", "nodejs", "express", "firebase"], "author": "ODenta Team", "license": "ISC", "dependencies": {"axios": "^1.7.7", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "firebase": "^11.10.0", "firebase-admin": "^12.7.0", "google-auth-library": "^10.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "socket.io": "^4.8.1", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.1.7"}}